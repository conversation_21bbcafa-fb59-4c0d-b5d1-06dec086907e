# Upload Assíncrono por Qualidade

Este documento descreve a implementação do upload assíncrono por qualidade, que permite que cada qualidade de vídeo seja enviada para o R2 assim que for finalizada, sem esperar todas as qualidades terminarem.

## Visão Geral

### Problema Anterior
- O sistema processava todas as qualidades (480p, 720p, 1080p) em paralelo
- Apenas após TODAS as qualidades terminarem, o upload para R2 era iniciado
- Isso causava atraso desnecessário, pois qualidades já finalizadas ficavam aguardando

### Solução Implementada
- Cada qualidade é enviada para R2 assim que seu processamento é concluído
- Upload acontece em paralelo com o processamento das outras qualidades
- Frontend recebe URLs das qualidades conforme ficam disponíveis

## Arquitetura

### Novos Campos de Progresso

```go
type QualityProgressInfo struct {
    Quality        string     `json:"quality"`
    Progress       float64    `json:"progress"`
    Status         string     `json:"status"`
    Message        string     `json:"message"`
    StartedAt      *time.Time `json:"started_at,omitempty"`
    CompletedAt    *time.Time `json:"completed_at,omitempty"`
    
    // Novos campos para upload
    UploadStatus   string     `json:"upload_status,omitempty"`   // "pending", "uploading", "completed", "failed"
    UploadProgress float64    `json:"upload_progress,omitempty"` // 0-100
    UploadMessage  string     `json:"upload_message,omitempty"`
    PlaylistURL    string     `json:"playlist_url,omitempty"`    // URL disponível após upload
}
```

### Callback de Upload

```go
type QualityUploadCallback func(outputID, quality string, qualityDir string) (string, error)
```

## Fluxo de Funcionamento

### 1. Processamento Paralelo
```
480p: [████████████████████████████████████████] 100% ✅ → Upload iniciado
720p: [██████████████████████████████          ] 75%  ⏳
1080p:[████████████████                        ] 40%  ⏳
```

### 2. Upload Assíncrono
```
480p: [████████████████████████████████████████] 100% ✅ → [████████████] Upload 100% ✅ → URL disponível
720p: [████████████████████████████████████████] 100% ✅ → Upload iniciado
1080p:[██████████████████████████████          ] 75%  ⏳
```

### 3. Resultado Final
```
480p: ✅ Disponível em: https://r2.example.com/videos/job123/480p/playlist.m3u8
720p: ✅ Disponível em: https://r2.example.com/videos/job123/720p/playlist.m3u8
1080p:✅ Disponível em: https://r2.example.com/videos/job123/1080p/playlist.m3u8
```

## Mensagens WebSocket

### Exemplo de Progresso com Upload

```json
{
  "type": "progress",
  "job_id": "job_238ae74aaf2dc09fabc8180a8157ec3f",
  "progress": 85.5,
  "status": "processing",
  "message": "Processando múltiplas qualidades",
  "current_quality": "1080p",
  "quality_progress": [
    {
      "quality": "480p",
      "progress": 100,
      "status": "completed",
      "message": "Processamento concluído",
      "upload_status": "completed",
      "upload_progress": 100,
      "upload_message": "Upload da qualidade 480p concluído",
      "playlist_url": "https://r2.example.com/videos/job123/480p/playlist.m3u8",
      "started_at": "2025-07-23T13:25:00Z",
      "completed_at": "2025-07-23T13:27:30Z"
    },
    {
      "quality": "720p",
      "progress": 100,
      "status": "completed",
      "message": "Processamento concluído",
      "upload_status": "uploading",
      "upload_progress": 45,
      "upload_message": "Fazendo upload da qualidade 720p",
      "started_at": "2025-07-23T13:25:00Z",
      "completed_at": "2025-07-23T13:28:15Z"
    },
    {
      "quality": "1080p",
      "progress": 85,
      "status": "processing",
      "message": "Processando: 22.3s de 26.3s",
      "upload_status": "pending",
      "upload_progress": 0,
      "upload_message": "",
      "started_at": "2025-07-23T13:25:00Z"
    }
  ],
  "timestamp": "2025-07-23T13:29:00Z"
}
```

## Benefícios

### 1. Disponibilidade Mais Rápida
- Qualidades ficam disponíveis assim que processadas
- Usuário pode começar a assistir em qualidade menor enquanto outras processam

### 2. Melhor Experiência do Usuário
- Progresso mais granular e informativo
- Feedback em tempo real sobre upload
- URLs disponíveis imediatamente

### 3. Eficiência de Recursos
- Upload paralelo ao processamento
- Melhor utilização da largura de banda
- Redução do tempo total de pipeline

## Implementação Frontend

### JavaScript Example

```javascript
const ws = new WebSocket('ws://localhost:8080/api/v1/ws/job_123');

ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  
  if (data.type === 'progress') {
    data.quality_progress.forEach(quality => {
      console.log(`${quality.quality}:`);
      console.log(`  Processamento: ${quality.progress}% - ${quality.status}`);
      console.log(`  Upload: ${quality.upload_progress}% - ${quality.upload_status}`);
      
      // URL disponível para reprodução
      if (quality.playlist_url) {
        console.log(`  ✅ Disponível: ${quality.playlist_url}`);
        // Adicionar qualidade ao player
        addQualityToPlayer(quality.quality, quality.playlist_url);
      }
    });
  }
};

function addQualityToPlayer(quality, url) {
  // Implementar lógica para adicionar qualidade ao player de vídeo
  // Exemplo com HLS.js ou Video.js
}
```

## Configuração

O upload assíncrono é automaticamente habilitado quando:
- `EnableStorage` está ativo
- `ProcessInParallel` está ativo  
- Múltiplas qualidades estão configuradas
- Provedor de storage (R2) está configurado

Não requer configuração adicional.
