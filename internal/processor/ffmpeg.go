package processor

import (
	"bufio"
	"fmt"
	"io"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"
)

// ProgressCallback é uma função callback para reportar progresso
type ProgressCallback func(progress float64, message string)

// QualityProgressInfo representa informações de progresso de uma qualidade
type QualityProgressInfo struct {
	Quality        string     `json:"quality"`
	Progress       float64    `json:"progress"`
	Status         string     `json:"status"`
	Message        string     `json:"message"`
	StartedAt      *time.Time `json:"started_at,omitempty"`
	CompletedAt    *time.Time `json:"completed_at,omitempty"`
	UploadStatus   string     `json:"upload_status,omitempty"`   // "pending", "uploading", "completed", "failed"
	UploadProgress float64    `json:"upload_progress,omitempty"` // 0-100
	UploadMessage  string     `json:"upload_message,omitempty"`
	PlaylistURL    string     `json:"playlist_url,omitempty"` // URL da playlist após upload
}

// QualityProgressCallback é uma função callback para reportar progresso de qualidades específicas
type QualityProgressCallback func(overallProgress float64, status, message, currentQuality string, qualityProgress []QualityProgressInfo)

// QualityUploadCallback é uma função callback para fazer upload de uma qualidade específica
type QualityUploadCallback func(outputID, quality string, qualityDir string) (string, error)

// QualityConfig define configurações para uma qualidade específica (importado do package queue)
type QualityConfig struct {
	Name      string `json:"name"`
	Width     int    `json:"width"`
	Height    int    `json:"height"`
	Bitrate   int    `json:"bitrate"`
	AudioRate int    `json:"audio_rate"`
	MaxRate   int    `json:"max_rate"`
	BufSize   int    `json:"buf_size"`
}

type ProcessorConfig struct {
	FFmpegPath        string          // Caminho para o executável do FFmpeg
	OutputDir         string          // Diretório de saída para arquivos processados
	TempDir           string          // Diretório temporário para processamento
	SegmentTime       int             // Duração de cada segmento HLS em segundos
	PlaylistType      string          // Tipo de playlist HLS (vod ou live)
	VideoCodec        string          // Codec de vídeo (h264, etc.)
	AudioCodec        string          // Codec de áudio (aac, etc.)
	VideoBitrates     []int           // Bitrates para streaming adaptativo (deprecated)
	Qualities         []QualityConfig // Configurações de qualidades
	ProcessInParallel bool            // Se deve processar qualidades em paralelo
}

// QualityProcessingResult contém o resultado do processamento de uma qualidade específica
type QualityProcessingResult struct {
	Quality        string        `json:"quality"`
	Success        bool          `json:"success"`
	PlaylistPath   string        `json:"playlist_path"`
	SegmentPaths   []string      `json:"segment_paths"`
	ProcessingTime time.Duration `json:"processing_time"`
	FileSize       int64         `json:"file_size"`
	SegmentCount   int           `json:"segment_count"`
	Error          string        `json:"error,omitempty"`
}

type ProcessingResult struct {
	Success            bool                      `json:"success"`
	MasterPlaylistPath string                    `json:"master_playlist_path"`
	PlaylistPath       string                    `json:"playlist_path"` // Deprecated
	SegmentPaths       []string                  `json:"segment_paths"` // Deprecated
	QualityResults     []QualityProcessingResult `json:"quality_results"`
	Duration           float64                   `json:"duration"`
	ProcessingTime     time.Duration             `json:"processing_time"`
	Error              string                    `json:"error,omitempty"`
}

type VideoProcessor struct {
	config *ProcessorConfig
}

func NewVideoProcessor(config *ProcessorConfig) *VideoProcessor {
	if config == nil {
		config = getDefaultConfig()
	}

	if config.FFmpegPath == "" {
		config.FFmpegPath = "ffmpeg" // Assume que está no PATH
	}

	return &VideoProcessor{
		config: config,
	}
}

func getDefaultConfig() *ProcessorConfig {
	return &ProcessorConfig{
		FFmpegPath:        "ffmpeg",
		OutputDir:         "./videos/processed",
		TempDir:           "./videos/temp",
		SegmentTime:       10, // 10 segundos por segmento
		PlaylistType:      "vod",
		VideoCodec:        "libx264",
		AudioCodec:        "aac",
		VideoBitrates:     []int{720, 480, 360}, // Resoluções para streaming adaptativo (deprecated)
		Qualities:         getDefaultQualities(),
		ProcessInParallel: true,
	}
}

// getDefaultQualities retorna as configurações padrão de qualidades
func getDefaultQualities() []QualityConfig {
	return []QualityConfig{
		{
			Name:      "480p",
			Width:     854,
			Height:    480,
			Bitrate:   1000, // 1 Mbps
			AudioRate: 128,  // 128 kbps
			MaxRate:   1200, // 1.2 Mbps
			BufSize:   2000, // 2 Mbps
		},
		{
			Name:      "720p",
			Width:     1280,
			Height:    720,
			Bitrate:   2500, // 2.5 Mbps
			AudioRate: 128,  // 128 kbps
			MaxRate:   3000, // 3 Mbps
			BufSize:   5000, // 5 Mbps
		},
		{
			Name:      "1080p",
			Width:     1920,
			Height:    1080,
			Bitrate:   5000,  // 5 Mbps
			AudioRate: 192,   // 192 kbps
			MaxRate:   6000,  // 6 Mbps
			BufSize:   10000, // 10 Mbps
		},
	}
}

func (vp *VideoProcessor) ProcessToHLS(inputPath, outputID string) (*ProcessingResult, error) {
	return vp.ProcessToHLSWithProgress(inputPath, outputID, nil)
}

// ProcessToHLSMultiQuality processa vídeo em múltiplas qualidades
func (vp *VideoProcessor) ProcessToHLSMultiQuality(inputPath, outputID string, qualityCallback QualityProgressCallback) (*ProcessingResult, error) {
	return vp.ProcessToHLSMultiQualityWithUpload(inputPath, outputID, qualityCallback, nil)
}

// ProcessToHLSMultiQualityWithUpload processa vídeo em múltiplas qualidades com upload assíncrono
func (vp *VideoProcessor) ProcessToHLSMultiQualityWithUpload(inputPath, outputID string, qualityCallback QualityProgressCallback, uploadCallback QualityUploadCallback) (*ProcessingResult, error) {
	startTime := time.Now()

	log.Printf("Iniciando processamento HLS multi-qualidade para: %s (ID: %s)", inputPath, outputID)

	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("arquivo de entrada não encontrado: %s", inputPath),
		}, err
	}

	// Obter duração do vídeo
	duration, err := vp.getVideoDuration(inputPath)
	if err != nil {
		log.Printf("Aviso: não foi possível obter duração do vídeo: %v", err)
		duration = 0
	}

	// Criar diretórios para cada qualidade
	if err := vp.createMultiQualityDirectories(outputID); err != nil {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("erro ao criar diretórios: %v", err),
		}, err
	}

	// Processar qualidades
	var qualityResults []QualityProcessingResult
	var qualityProgress []QualityProgressInfo

	// Inicializar progresso das qualidades
	for _, quality := range vp.config.Qualities {
		qualityProgress = append(qualityProgress, QualityProgressInfo{
			Quality:      quality.Name,
			Progress:     0,
			Status:       "pending",
			Message:      fmt.Sprintf("Aguardando processamento %s", quality.Name),
			UploadStatus: "pending",
		})
	}

	if vp.config.ProcessInParallel {
		qualityResults, err = vp.processQualitiesInParallelWithUpload(inputPath, outputID, duration, qualityCallback, uploadCallback, qualityProgress)
	} else {
		qualityResults, err = vp.processQualitiesSequentiallyWithUpload(inputPath, outputID, duration, qualityCallback, uploadCallback, qualityProgress)
	}

	if err != nil {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("erro no processamento de qualidades: %v", err),
		}, err
	}

	// Criar playlist master
	masterPlaylistPath, err := vp.createMasterPlaylist(outputID, qualityResults)
	if err != nil {
		log.Printf("Aviso: erro ao criar playlist master: %v", err)
	}

	processingTime := time.Since(startTime)

	// Verificar se pelo menos uma qualidade foi processada com sucesso
	success := false
	for _, qr := range qualityResults {
		if qr.Success {
			success = true
			break
		}
	}

	result := &ProcessingResult{
		Success:            success,
		MasterPlaylistPath: masterPlaylistPath,
		QualityResults:     qualityResults,
		Duration:           duration,
		ProcessingTime:     processingTime,
	}

	// Manter compatibilidade com versão anterior (usar primeira qualidade bem-sucedida)
	for _, qr := range qualityResults {
		if qr.Success {
			result.PlaylistPath = qr.PlaylistPath
			result.SegmentPaths = qr.SegmentPaths
			break
		}
	}

	log.Printf("Processamento HLS multi-qualidade concluído em %v", processingTime)

	return result, nil
}

func (vp *VideoProcessor) ProcessToHLSWithProgress(inputPath, outputID string, progressCallback ProgressCallback) (*ProcessingResult, error) {
	startTime := time.Now()

	log.Printf("Iniciando processamento HLS para: %s (ID: %s)", inputPath, outputID)

	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("arquivo de entrada não encontrado: %s", inputPath),
		}, err
	}

	if err := vp.createDirectories(outputID); err != nil {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("erro ao criar diretórios: %v", err),
		}, err
	}

	// Obter duração do vídeo primeiro para calcular progresso
	duration, err := vp.getVideoDuration(inputPath)
	if err != nil {
		log.Printf("Aviso: não foi possível obter duração do vídeo: %v", err)
		duration = 0
	}

	outputDir := filepath.Join(vp.config.OutputDir, outputID)
	playlistPath := filepath.Join(outputDir, "playlist.m3u8")
	segmentPattern := filepath.Join(outputDir, "segment_%03d.ts")

	cmd := vp.buildFFmpegCommand(inputPath, playlistPath, segmentPattern)

	log.Printf("Executando comando FFmpeg: %s", strings.Join(cmd.Args, " "))

	// Executar FFmpeg com monitoramento de progresso
	err = vp.runFFmpegWithProgress(cmd, duration, progressCallback)
	if err != nil {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("erro no processamento FFmpeg: %v", err),
		}, err
	}

	segmentPaths, err := vp.listGeneratedSegments(outputDir)
	if err != nil {
		log.Printf("Aviso: erro ao listar segmentos: %v", err)
	}

	processingTime := time.Since(startTime)

	result := &ProcessingResult{
		Success:        true,
		PlaylistPath:   playlistPath,
		SegmentPaths:   segmentPaths,
		Duration:       duration,
		ProcessingTime: processingTime,
	}

	log.Printf("Processamento HLS concluído com sucesso em %v", processingTime)

	return result, nil
}

func (vp *VideoProcessor) buildFFmpegCommand(inputPath, playlistPath, segmentPattern string) *exec.Cmd {
	args := []string{
		"-i", inputPath, // Arquivo de entrada
		"-c:v", vp.config.VideoCodec, // Codec de vídeo
		"-c:a", vp.config.AudioCodec, // Codec de áudio
		"-hls_time", fmt.Sprintf("%d", vp.config.SegmentTime), // Duração dos segmentos
		"-hls_playlist_type", vp.config.PlaylistType, // Tipo de playlist
		"-hls_segment_filename", segmentPattern, // Padrão dos segmentos
		"-f", "hls", // Formato de saída HLS
		"-y",         // Sobrescrever arquivos existentes
		playlistPath, // Arquivo de playlist de saída
	}

	return exec.Command(vp.config.FFmpegPath, args...)
}

func (vp *VideoProcessor) createDirectories(outputID string) error {
	outputDir := filepath.Join(vp.config.OutputDir, outputID)

	if err := os.MkdirAll(outputDir, 0755); err != nil {
		return fmt.Errorf("erro ao criar diretório de saída: %v", err)
	}

	if err := os.MkdirAll(vp.config.TempDir, 0755); err != nil {
		return fmt.Errorf("erro ao criar diretório temporário: %v", err)
	}

	return nil
}

func (vp *VideoProcessor) getVideoDuration(inputPath string) (float64, error) {
	cmd := exec.Command("ffprobe",
		"-v", "quiet",
		"-show_entries", "format=duration",
		"-of", "csv=p=0",
		inputPath,
	)

	output, err := cmd.Output()
	if err != nil {
		return 0, err
	}

	var duration float64
	if _, err := fmt.Sscanf(strings.TrimSpace(string(output)), "%f", &duration); err != nil {
		return 0, err
	}

	return duration, nil
}

func (vp *VideoProcessor) listGeneratedSegments(outputDir string) ([]string, error) {
	var segments []string

	files, err := os.ReadDir(outputDir)
	if err != nil {
		return nil, err
	}

	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".ts") {
			segments = append(segments, filepath.Join(outputDir, file.Name()))
		}
	}

	return segments, nil
}

func (vp *VideoProcessor) ValidateFFmpeg() error {
	cmd := exec.Command(vp.config.FFmpegPath, "-version")
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("FFmpeg não encontrado ou não executável: %v", err)
	}

	log.Printf("FFmpeg validado com sucesso")
	return nil
}

func (vp *VideoProcessor) CleanupTempFiles(outputID string) error {
	tempDir := filepath.Join(vp.config.TempDir, outputID)

	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		return nil // Diretório não existe, nada para limpar
	}

	if err := os.RemoveAll(tempDir); err != nil {
		return fmt.Errorf("erro ao limpar arquivos temporários: %v", err)
	}

	log.Printf("Arquivos temporários limpos para ID: %s", outputID)
	return nil
}

func (vp *VideoProcessor) GetProcessedVideoInfo(outputID string) (*ProcessingResult, error) {
	outputDir := filepath.Join(vp.config.OutputDir, outputID)
	playlistPath := filepath.Join(outputDir, "playlist.m3u8")

	if _, err := os.Stat(playlistPath); os.IsNotExist(err) {
		return &ProcessingResult{
			Success: false,
			Error:   "vídeo processado não encontrado",
		}, err
	}

	segmentPaths, err := vp.listGeneratedSegments(outputDir)
	if err != nil {
		return &ProcessingResult{
			Success: false,
			Error:   fmt.Sprintf("erro ao listar segmentos: %v", err),
		}, err
	}

	return &ProcessingResult{
		Success:      true,
		PlaylistPath: playlistPath,
		SegmentPaths: segmentPaths,
	}, nil
}

// runFFmpegWithProgress executa FFmpeg e monitora o progresso
func (vp *VideoProcessor) runFFmpegWithProgress(cmd *exec.Cmd, totalDuration float64, progressCallback ProgressCallback) error {
	// Adicionar flags para obter informações de progresso do FFmpeg
	cmd.Args = append(cmd.Args[:len(cmd.Args)-1], "-progress", "pipe:2", cmd.Args[len(cmd.Args)-1])

	stderr, err := cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("erro ao criar pipe stderr: %v", err)
	}

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("erro ao iniciar FFmpeg: %v", err)
	}

	// Monitorar progresso se callback foi fornecido
	if progressCallback != nil && totalDuration > 0 {
		go vp.monitorProgress(stderr, totalDuration, progressCallback)
	}

	return cmd.Wait()
}

// createMultiQualityDirectories cria diretórios para cada qualidade
func (vp *VideoProcessor) createMultiQualityDirectories(outputID string) error {
	baseDir := filepath.Join(vp.config.OutputDir, outputID)

	// Criar diretório base
	if err := os.MkdirAll(baseDir, 0755); err != nil {
		return fmt.Errorf("erro ao criar diretório base: %v", err)
	}

	// Criar diretório para cada qualidade
	for _, quality := range vp.config.Qualities {
		qualityDir := filepath.Join(baseDir, quality.Name)
		if err := os.MkdirAll(qualityDir, 0755); err != nil {
			return fmt.Errorf("erro ao criar diretório para qualidade %s: %v", quality.Name, err)
		}
	}

	return nil
}

// processQualitiesInParallel processa todas as qualidades em paralelo usando goroutines
func (vp *VideoProcessor) processQualitiesInParallel(inputPath, outputID string, duration float64, qualityCallback QualityProgressCallback, qualityProgress []QualityProgressInfo) ([]QualityProcessingResult, error) {
	var results []QualityProcessingResult
	var mu sync.Mutex
	var wg sync.WaitGroup

	// Canal para comunicação de progresso
	progressChan := make(chan QualityProgressInfo, len(vp.config.Qualities))

	// Goroutine para monitorar progresso
	go func() {
		for progressUpdate := range progressChan {
			mu.Lock()
			// Atualizar progresso na lista
			for i := range qualityProgress {
				if qualityProgress[i].Quality == progressUpdate.Quality {
					qualityProgress[i] = progressUpdate
					break
				}
			}

			// Calcular progresso geral
			overallProgress := vp.calculateOverallProgress(qualityProgress)
			currentQuality := vp.getCurrentProcessingQuality(qualityProgress)

			if qualityCallback != nil {
				qualityCallback(overallProgress, "processing", "Processando múltiplas qualidades", currentQuality, qualityProgress)
			}
			mu.Unlock()
		}
	}()

	// Processar cada qualidade em uma goroutine separada
	for i, quality := range vp.config.Qualities {
		wg.Add(1)
		go func(idx int, q QualityConfig) {
			defer wg.Done()

			result := vp.processQuality(inputPath, outputID, q, duration, func(progress float64, message string) {
				progressUpdate := QualityProgressInfo{
					Quality:  q.Name,
					Progress: progress,
					Status:   "processing",
					Message:  message,
				}
				if progress == 0 {
					now := time.Now()
					progressUpdate.StartedAt = &now
				} else if progress >= 100 {
					progressUpdate.Status = "completed"
					now := time.Now()
					progressUpdate.CompletedAt = &now
				}

				select {
				case progressChan <- progressUpdate:
				default:
					// Canal cheio, ignorar atualização
				}
			})

			mu.Lock()
			results = append(results, result)
			mu.Unlock()
		}(i, quality)
	}

	// Aguardar conclusão de todas as goroutines
	wg.Wait()
	close(progressChan)

	return results, nil
}

// processQualitiesInParallelWithUpload processa todas as qualidades em paralelo com upload assíncrono
func (vp *VideoProcessor) processQualitiesInParallelWithUpload(inputPath, outputID string, duration float64, qualityCallback QualityProgressCallback, uploadCallback QualityUploadCallback, qualityProgress []QualityProgressInfo) ([]QualityProcessingResult, error) {
	var results []QualityProcessingResult
	var mu sync.Mutex
	var wg sync.WaitGroup
	var uploadWg sync.WaitGroup // WaitGroup separado para uploads

	// Canal para comunicação de progresso
	progressChan := make(chan QualityProgressInfo, len(vp.config.Qualities)*2) // Dobrar o buffer para incluir updates de upload

	// Goroutine para monitorar progresso
	go func() {
		for progressUpdate := range progressChan {
			mu.Lock()
			// Atualizar progresso na lista
			for i := range qualityProgress {
				if qualityProgress[i].Quality == progressUpdate.Quality {
					// Manter informações existentes e atualizar apenas campos relevantes
					if progressUpdate.Progress > 0 {
						qualityProgress[i].Progress = progressUpdate.Progress
					}
					if progressUpdate.Status != "" {
						qualityProgress[i].Status = progressUpdate.Status
					}
					if progressUpdate.Message != "" {
						qualityProgress[i].Message = progressUpdate.Message
					}
					if progressUpdate.StartedAt != nil {
						qualityProgress[i].StartedAt = progressUpdate.StartedAt
					}
					if progressUpdate.CompletedAt != nil {
						qualityProgress[i].CompletedAt = progressUpdate.CompletedAt
					}
					if progressUpdate.UploadStatus != "" {
						qualityProgress[i].UploadStatus = progressUpdate.UploadStatus
					}
					if progressUpdate.UploadProgress > 0 {
						qualityProgress[i].UploadProgress = progressUpdate.UploadProgress
					}
					if progressUpdate.UploadMessage != "" {
						qualityProgress[i].UploadMessage = progressUpdate.UploadMessage
					}
					if progressUpdate.PlaylistURL != "" {
						qualityProgress[i].PlaylistURL = progressUpdate.PlaylistURL
					}
					break
				}
			}

			// Calcular progresso geral considerando uploads
			overallProgress := vp.calculateOverallProgressWithUploads(qualityProgress, uploadCallback != nil)
			currentQuality := vp.getCurrentProcessingQuality(qualityProgress)

			// Verificar se tudo está completo (processamento + uploads)
			allComplete := vp.areAllQualitiesComplete(qualityProgress, uploadCallback != nil)
			status := "processing"
			message := "Processando múltiplas qualidades"

			if allComplete {
				status = "completed"
				message = "Processamento e upload concluídos"
			}

			if qualityCallback != nil {
				qualityCallback(overallProgress, status, message, currentQuality, qualityProgress)
			}
			mu.Unlock()
		}
	}()

	// Processar cada qualidade em uma goroutine separada
	for i, quality := range vp.config.Qualities {
		wg.Add(1)
		go func(idx int, q QualityConfig) {
			defer wg.Done()

			result := vp.processQuality(inputPath, outputID, q, duration, func(progress float64, message string) {
				progressUpdate := QualityProgressInfo{
					Quality:  q.Name,
					Progress: progress,
					Status:   "processing",
					Message:  message,
				}
				if progress == 0 {
					now := time.Now()
					progressUpdate.StartedAt = &now
				} else if progress >= 100 {
					progressUpdate.Status = "completed"
					now := time.Now()
					progressUpdate.CompletedAt = &now
				}

				select {
				case progressChan <- progressUpdate:
				default:
					// Canal cheio, ignorar atualização
				}
			})

			// Se o processamento foi bem-sucedido e temos callback de upload, fazer upload
			if result.Success && uploadCallback != nil {
				uploadWg.Add(1) // Adicionar ao WaitGroup de uploads
				go func() {
					defer uploadWg.Done() // Marcar upload como concluído

					// Reportar início do upload
					uploadUpdate := QualityProgressInfo{
						Quality:        q.Name,
						UploadStatus:   "uploading",
						UploadProgress: 0,
						UploadMessage:  fmt.Sprintf("Iniciando upload da qualidade %s", q.Name),
					}
					select {
					case progressChan <- uploadUpdate:
					default:
					}

					// Fazer upload
					qualityDir := filepath.Join(vp.config.OutputDir, outputID, q.Name)
					playlistURL, err := uploadCallback(outputID, q.Name, qualityDir)

					// Reportar resultado do upload
					if err != nil {
						uploadUpdate = QualityProgressInfo{
							Quality:        q.Name,
							UploadStatus:   "failed",
							UploadProgress: 0,
							UploadMessage:  fmt.Sprintf("Erro no upload: %v", err),
						}
					} else {
						uploadUpdate = QualityProgressInfo{
							Quality:        q.Name,
							UploadStatus:   "completed",
							UploadProgress: 100,
							UploadMessage:  fmt.Sprintf("Upload da qualidade %s concluído", q.Name),
							PlaylistURL:    playlistURL,
						}
					}

					select {
					case progressChan <- uploadUpdate:
					default:
					}
				}()
			}

			mu.Lock()
			results = append(results, result)
			mu.Unlock()
		}(i, quality)
	}

	// Aguardar conclusão de todas as goroutines de processamento
	wg.Wait()

	// Se temos uploads, aguardar conclusão de todos os uploads
	if uploadCallback != nil {
		uploadWg.Wait()
	}

	close(progressChan)

	return results, nil
}

// processQualitiesSequentially processa qualidades uma por vez
func (vp *VideoProcessor) processQualitiesSequentially(inputPath, outputID string, duration float64, qualityCallback QualityProgressCallback, qualityProgress []QualityProgressInfo) ([]QualityProcessingResult, error) {
	var results []QualityProcessingResult

	for i, quality := range vp.config.Qualities {
		// Atualizar status para "processing"
		qualityProgress[i].Status = "processing"
		qualityProgress[i].Message = fmt.Sprintf("Processando %s", quality.Name)
		now := time.Now()
		qualityProgress[i].StartedAt = &now

		if qualityCallback != nil {
			overallProgress := vp.calculateOverallProgress(qualityProgress)
			qualityCallback(overallProgress, "processing", fmt.Sprintf("Processando %s", quality.Name), quality.Name, qualityProgress)
		}

		result := vp.processQuality(inputPath, outputID, quality, duration, func(progress float64, message string) {
			qualityProgress[i].Progress = progress
			qualityProgress[i].Message = message

			if qualityCallback != nil {
				overallProgress := vp.calculateOverallProgress(qualityProgress)
				qualityCallback(overallProgress, "processing", message, quality.Name, qualityProgress)
			}
		})

		// Atualizar status final
		if result.Success {
			qualityProgress[i].Status = "completed"
			qualityProgress[i].Progress = 100
		} else {
			qualityProgress[i].Status = "failed"
		}
		now = time.Now()
		qualityProgress[i].CompletedAt = &now

		results = append(results, result)
	}

	return results, nil
}

// processQualitiesSequentiallyWithUpload processa qualidades uma por vez com upload assíncrono
func (vp *VideoProcessor) processQualitiesSequentiallyWithUpload(inputPath, outputID string, duration float64, qualityCallback QualityProgressCallback, uploadCallback QualityUploadCallback, qualityProgress []QualityProgressInfo) ([]QualityProcessingResult, error) {
	var results []QualityProcessingResult

	for i, quality := range vp.config.Qualities {
		// Atualizar status para "processing"
		qualityProgress[i].Status = "processing"
		qualityProgress[i].Message = fmt.Sprintf("Processando %s", quality.Name)
		now := time.Now()
		qualityProgress[i].StartedAt = &now

		if qualityCallback != nil {
			overallProgress := vp.calculateOverallProgress(qualityProgress)
			qualityCallback(overallProgress, "processing", fmt.Sprintf("Processando %s", quality.Name), quality.Name, qualityProgress)
		}

		result := vp.processQuality(inputPath, outputID, quality, duration, func(progress float64, message string) {
			qualityProgress[i].Progress = progress
			qualityProgress[i].Message = message

			if qualityCallback != nil {
				overallProgress := vp.calculateOverallProgress(qualityProgress)
				qualityCallback(overallProgress, "processing", message, quality.Name, qualityProgress)
			}
		})

		// Atualizar status final
		if result.Success {
			qualityProgress[i].Status = "completed"
			qualityProgress[i].Progress = 100

			// Se temos callback de upload, fazer upload assíncrono
			if uploadCallback != nil {
				go func(qualityIndex int, q QualityConfig) {
					// Reportar início do upload
					qualityProgress[qualityIndex].UploadStatus = "uploading"
					qualityProgress[qualityIndex].UploadProgress = 0
					qualityProgress[qualityIndex].UploadMessage = fmt.Sprintf("Iniciando upload da qualidade %s", q.Name)

					if qualityCallback != nil {
						overallProgress := vp.calculateOverallProgress(qualityProgress)
						qualityCallback(overallProgress, "processing", qualityProgress[qualityIndex].UploadMessage, q.Name, qualityProgress)
					}

					// Fazer upload
					qualityDir := filepath.Join(vp.config.OutputDir, outputID, q.Name)
					playlistURL, err := uploadCallback(outputID, q.Name, qualityDir)

					// Reportar resultado do upload
					if err != nil {
						qualityProgress[qualityIndex].UploadStatus = "failed"
						qualityProgress[qualityIndex].UploadProgress = 0
						qualityProgress[qualityIndex].UploadMessage = fmt.Sprintf("Erro no upload: %v", err)
					} else {
						qualityProgress[qualityIndex].UploadStatus = "completed"
						qualityProgress[qualityIndex].UploadProgress = 100
						qualityProgress[qualityIndex].UploadMessage = fmt.Sprintf("Upload da qualidade %s concluído", q.Name)
						qualityProgress[qualityIndex].PlaylistURL = playlistURL
					}

					if qualityCallback != nil {
						overallProgress := vp.calculateOverallProgress(qualityProgress)
						qualityCallback(overallProgress, "processing", qualityProgress[qualityIndex].UploadMessage, q.Name, qualityProgress)
					}
				}(i, quality)
			}
		} else {
			qualityProgress[i].Status = "failed"
		}
		now = time.Now()
		qualityProgress[i].CompletedAt = &now

		results = append(results, result)
	}

	return results, nil
}

// calculateOverallProgress calcula o progresso geral baseado no progresso de cada qualidade
func (vp *VideoProcessor) calculateOverallProgress(qualityProgress []QualityProgressInfo) float64 {
	if len(qualityProgress) == 0 {
		return 0
	}

	totalProgress := 0.0
	for _, qp := range qualityProgress {
		totalProgress += qp.Progress
	}

	return totalProgress / float64(len(qualityProgress))
}

// calculateOverallProgressWithUploads calcula o progresso geral considerando processamento e uploads
func (vp *VideoProcessor) calculateOverallProgressWithUploads(qualityProgress []QualityProgressInfo, hasUploads bool) float64 {
	if len(qualityProgress) == 0 {
		return 0
	}

	if !hasUploads {
		// Se não há uploads, usar cálculo tradicional
		return vp.calculateOverallProgress(qualityProgress)
	}

	// Com uploads, considerar tanto processamento quanto upload
	totalProgress := 0.0
	for _, qp := range qualityProgress {
		// Processamento vale 70% do progresso total
		processingWeight := qp.Progress * 0.7

		// Upload vale 30% do progresso total
		uploadWeight := 0.0
		if qp.UploadStatus == "completed" {
			uploadWeight = 30.0
		} else if qp.UploadStatus == "uploading" {
			uploadWeight = qp.UploadProgress * 0.3
		}

		qualityTotalProgress := processingWeight + uploadWeight
		totalProgress += qualityTotalProgress
	}

	return totalProgress / float64(len(qualityProgress))
}

// areAllQualitiesComplete verifica se todas as qualidades estão completamente processadas e enviadas
func (vp *VideoProcessor) areAllQualitiesComplete(qualityProgress []QualityProgressInfo, hasUploads bool) bool {
	if len(qualityProgress) == 0 {
		return false
	}

	for _, qp := range qualityProgress {
		// Verificar se o processamento está completo
		if qp.Status != "completed" {
			return false
		}

		// Se há uploads, verificar se o upload também está completo
		if hasUploads && qp.UploadStatus != "completed" {
			return false
		}
	}

	return true
}

// getCurrentProcessingQuality retorna a qualidade que está sendo processada atualmente
func (vp *VideoProcessor) getCurrentProcessingQuality(qualityProgress []QualityProgressInfo) string {
	for _, qp := range qualityProgress {
		if qp.Status == "processing" {
			return qp.Quality
		}
	}
	return ""
}

// processQuality processa uma qualidade específica
func (vp *VideoProcessor) processQuality(inputPath, outputID string, quality QualityConfig, duration float64, progressCallback ProgressCallback) QualityProcessingResult {
	startTime := time.Now()

	// Definir caminhos de saída
	qualityDir := filepath.Join(vp.config.OutputDir, outputID, quality.Name)
	playlistPath := filepath.Join(qualityDir, "playlist.m3u8")
	segmentPattern := filepath.Join(qualityDir, "segment_%03d.ts")

	// Construir comando FFmpeg para a qualidade específica
	cmd := vp.buildQualityFFmpegCommand(inputPath, playlistPath, segmentPattern, quality)

	log.Printf("Processando qualidade %s: %s", quality.Name, strings.Join(cmd.Args, " "))

	// Executar FFmpeg com monitoramento de progresso
	err := vp.runFFmpegWithProgress(cmd, duration, progressCallback)
	if err != nil {
		return QualityProcessingResult{
			Quality: quality.Name,
			Success: false,
			Error:   fmt.Sprintf("erro no processamento FFmpeg para %s: %v", quality.Name, err),
		}
	}

	// Listar segmentos gerados
	segmentPaths, err := vp.listGeneratedSegments(qualityDir)
	if err != nil {
		log.Printf("Aviso: erro ao listar segmentos para %s: %v", quality.Name, err)
	}

	// Calcular tamanho dos arquivos
	fileSize := vp.calculateDirectorySize(qualityDir)

	processingTime := time.Since(startTime)

	return QualityProcessingResult{
		Quality:        quality.Name,
		Success:        true,
		PlaylistPath:   playlistPath,
		SegmentPaths:   segmentPaths,
		ProcessingTime: processingTime,
		FileSize:       fileSize,
		SegmentCount:   len(segmentPaths),
	}
}

// buildQualityFFmpegCommand constrói comando FFmpeg para uma qualidade específica
func (vp *VideoProcessor) buildQualityFFmpegCommand(inputPath, playlistPath, segmentPattern string, quality QualityConfig) *exec.Cmd {
	args := []string{
		"-i", inputPath, // Arquivo de entrada
		"-c:v", vp.config.VideoCodec, // Codec de vídeo
		"-c:a", vp.config.AudioCodec, // Codec de áudio
		"-vf", fmt.Sprintf("scale=%d:%d", quality.Width, quality.Height), // Escala de vídeo
		"-b:v", fmt.Sprintf("%dk", quality.Bitrate), // Bitrate de vídeo
		"-maxrate", fmt.Sprintf("%dk", quality.MaxRate), // Bitrate máximo
		"-bufsize", fmt.Sprintf("%dk", quality.BufSize), // Buffer size
		"-b:a", fmt.Sprintf("%dk", quality.AudioRate), // Bitrate de áudio
		"-hls_time", fmt.Sprintf("%d", vp.config.SegmentTime), // Duração dos segmentos
		"-hls_playlist_type", vp.config.PlaylistType, // Tipo de playlist
		"-hls_segment_filename", segmentPattern, // Padrão dos segmentos
		"-f", "hls", // Formato de saída HLS
		"-y",         // Sobrescrever arquivos existentes
		playlistPath, // Arquivo de playlist de saída
	}

	return exec.Command(vp.config.FFmpegPath, args...)
}

// calculateDirectorySize calcula o tamanho total de um diretório
func (vp *VideoProcessor) calculateDirectorySize(dirPath string) int64 {
	var size int64

	err := filepath.Walk(dirPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() {
			size += info.Size()
		}
		return nil
	})

	if err != nil {
		log.Printf("Erro ao calcular tamanho do diretório %s: %v", dirPath, err)
		return 0
	}

	return size
}

// createMasterPlaylist cria a playlist master HLS
func (vp *VideoProcessor) createMasterPlaylist(outputID string, qualityResults []QualityProcessingResult) (string, error) {
	baseDir := filepath.Join(vp.config.OutputDir, outputID)
	masterPlaylistPath := filepath.Join(baseDir, "master.m3u8")

	var content strings.Builder
	content.WriteString("#EXTM3U\n")
	content.WriteString("#EXT-X-VERSION:3\n\n")

	// Adicionar cada qualidade bem-sucedida à playlist master
	for _, result := range qualityResults {
		if !result.Success {
			continue
		}

		// Obter configuração da qualidade
		var quality QualityConfig
		for _, q := range vp.config.Qualities {
			if q.Name == result.Quality {
				quality = q
				break
			}
		}

		// Adicionar entrada da qualidade
		content.WriteString(fmt.Sprintf("#EXT-X-STREAM-INF:BANDWIDTH=%d,RESOLUTION=%dx%d\n",
			quality.Bitrate*1000, quality.Width, quality.Height))
		content.WriteString(fmt.Sprintf("%s/playlist.m3u8\n\n", result.Quality))
	}

	// Escrever arquivo
	err := os.WriteFile(masterPlaylistPath, []byte(content.String()), 0644)
	if err != nil {
		return "", fmt.Errorf("erro ao criar playlist master: %v", err)
	}

	log.Printf("Playlist master criada: %s", masterPlaylistPath)
	return masterPlaylistPath, nil
}

// monitorProgress monitora a saída do FFmpeg para extrair informações de progresso
func (vp *VideoProcessor) monitorProgress(stderr io.ReadCloser, totalDuration float64, progressCallback ProgressCallback) {
	defer stderr.Close()

	scanner := bufio.NewScanner(stderr)
	timeRegex := regexp.MustCompile(`out_time_ms=(\d+)`)

	for scanner.Scan() {
		line := scanner.Text()

		// Procurar por informações de tempo no formato out_time_ms
		if matches := timeRegex.FindStringSubmatch(line); len(matches) > 1 {
			if timeMicros, err := strconv.ParseInt(matches[1], 10, 64); err == nil {
				currentTime := float64(timeMicros) / 1000000.0 // Converter de microssegundos para segundos

				if totalDuration > 0 {
					progress := (currentTime / totalDuration) * 100
					if progress > 100 {
						progress = 100
					}

					message := fmt.Sprintf("Processando: %.1fs de %.1fs", currentTime, totalDuration)
					progressCallback(progress, message)
				}
			}
		}
	}

	if err := scanner.Err(); err != nil {
		log.Printf("Erro ao ler saída do FFmpeg: %v", err)
	}
}
