package websocket

import (
	"encoding/json"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// QualityProgress representa o progresso de uma qualidade específica
type QualityProgress struct {
	Quality        string     `json:"quality"`                   // Nome da qualidade (480p, 720p, 1080p)
	Progress       float64    `json:"progress"`                  // Progresso da qualidade (0-100)
	Status         string     `json:"status"`                    // Status da qualidade (processing, completed, failed)
	Message        string     `json:"message"`                   // Mensagem específica da qualidade
	StartedAt      *time.Time `json:"started_at,omitempty"`      // Quando iniciou o processamento
	CompletedAt    *time.Time `json:"completed_at,omitempty"`    // Quando completou o processamento
	UploadStatus   string     `json:"upload_status,omitempty"`   // Status do upload (pending, uploading, completed, failed)
	UploadProgress float64    `json:"upload_progress,omitempty"` // Progresso do upload (0-100)
	UploadMessage  string     `json:"upload_message,omitempty"`  // Mensagem específica do upload
	PlaylistURL    string     `json:"playlist_url,omitempty"`    // URL da playlist após upload
}

// ProgressMessage representa uma mensagem de progresso
type ProgressMessage struct {
	Type            string                 `json:"type"`
	JobID           string                 `json:"job_id"`
	Progress        float64                `json:"progress"`                   // Progresso geral em porcentagem (0-100)
	Status          string                 `json:"status"`                     // Status atual do job
	Message         string                 `json:"message"`                    // Mensagem descritiva
	CurrentQuality  string                 `json:"current_quality,omitempty"`  // Qualidade sendo processada atualmente
	QualityProgress []QualityProgress      `json:"quality_progress,omitempty"` // Progresso de cada qualidade
	Timestamp       time.Time              `json:"timestamp"`                  // Timestamp da mensagem
	Data            map[string]interface{} `json:"data,omitempty"`             // Dados adicionais
}

// Client representa um cliente WebSocket conectado
type Client struct {
	hub    *Hub
	conn   *websocket.Conn
	send   chan []byte
	jobID  string // ID do job que o cliente está monitorando
	userID string // ID do usuário (opcional)
}

// Hub mantém o conjunto de clientes ativos e transmite mensagens para eles
type Hub struct {
	// Clientes registrados
	clients map[*Client]bool

	// Clientes organizados por job ID para broadcast direcionado
	jobClients map[string]map[*Client]bool

	// Canal para registrar clientes
	register chan *Client

	// Canal para desregistrar clientes
	unregister chan *Client

	// Canal para mensagens de broadcast
	broadcast chan []byte

	// Canal para mensagens direcionadas a um job específico
	jobBroadcast chan *JobMessage

	// Mutex para operações thread-safe
	mu sync.RWMutex

	// Configurações
	config *HubConfig
}

// JobMessage representa uma mensagem direcionada a um job específico
type JobMessage struct {
	JobID   string `json:"job_id"`
	Message []byte `json:"message"`
}

// HubConfig contém configurações do hub
type HubConfig struct {
	WriteWait      time.Duration // Tempo limite para escrita
	PongWait       time.Duration // Tempo limite para pong
	PingPeriod     time.Duration // Período de ping
	MaxMessageSize int64         // Tamanho máximo da mensagem
}

// Upgrader para WebSocket
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Em produção, você deve verificar a origem adequadamente
		return true
	},
}

// NewHub cria um novo hub WebSocket
func NewHub(config *HubConfig) *Hub {
	if config == nil {
		config = &HubConfig{
			WriteWait:      10 * time.Second,
			PongWait:       60 * time.Second,
			PingPeriod:     54 * time.Second,
			MaxMessageSize: 512,
		}
	}

	return &Hub{
		clients:      make(map[*Client]bool),
		jobClients:   make(map[string]map[*Client]bool),
		register:     make(chan *Client),
		unregister:   make(chan *Client),
		broadcast:    make(chan []byte),
		jobBroadcast: make(chan *JobMessage),
		config:       config,
	}
}

// Run inicia o hub WebSocket
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastToAll(message)

		case jobMessage := <-h.jobBroadcast:
			h.broadcastToJob(jobMessage.JobID, jobMessage.Message)
		}
	}
}

// registerClient registra um novo cliente
func (h *Hub) registerClient(client *Client) {
	h.mu.Lock()
	defer h.mu.Unlock()

	h.clients[client] = true

	// Adicionar cliente ao mapa de jobs se tiver jobID
	if client.jobID != "" {
		if h.jobClients[client.jobID] == nil {
			h.jobClients[client.jobID] = make(map[*Client]bool)
		}
		h.jobClients[client.jobID][client] = true
	}

	log.Printf("Cliente WebSocket registrado. JobID: %s, Total clientes: %d",
		client.jobID, len(h.clients))
}

// unregisterClient desregistra um cliente
func (h *Hub) unregisterClient(client *Client) {
	h.mu.Lock()
	defer h.mu.Unlock()

	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		close(client.send)

		// Remover cliente do mapa de jobs
		if client.jobID != "" {
			if jobClients, exists := h.jobClients[client.jobID]; exists {
				delete(jobClients, client)
				if len(jobClients) == 0 {
					delete(h.jobClients, client.jobID)
				}
			}
		}

		log.Printf("Cliente WebSocket desregistrado. JobID: %s, Total clientes: %d",
			client.jobID, len(h.clients))
	}
}

// broadcastToAll envia mensagem para todos os clientes
func (h *Hub) broadcastToAll(message []byte) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	for client := range h.clients {
		select {
		case client.send <- message:
		default:
			close(client.send)
			delete(h.clients, client)
		}
	}
}

// broadcastToJob envia mensagem para clientes de um job específico
func (h *Hub) broadcastToJob(jobID string, message []byte) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	if jobClients, exists := h.jobClients[jobID]; exists {
		for client := range jobClients {
			select {
			case client.send <- message:
			default:
				close(client.send)
				delete(h.clients, client)
				delete(jobClients, client)
			}
		}
	}
}

// SendProgressUpdate envia atualização de progresso para um job específico
func (h *Hub) SendProgressUpdate(jobID string, progress float64, status, message string, data map[string]interface{}) {
	progressMsg := &ProgressMessage{
		Type:      "progress",
		JobID:     jobID,
		Progress:  progress,
		Status:    status,
		Message:   message,
		Timestamp: time.Now(),
		Data:      data,
	}

	messageBytes, err := json.Marshal(progressMsg)
	if err != nil {
		log.Printf("Erro ao serializar mensagem de progresso: %v", err)
		return
	}

	select {
	case h.jobBroadcast <- &JobMessage{JobID: jobID, Message: messageBytes}:
	default:
		log.Printf("Canal de broadcast cheio, mensagem descartada para job %s", jobID)
	}
}

// SendQualityProgressUpdate envia atualização de progresso com informações de qualidade
func (h *Hub) SendQualityProgressUpdate(jobID string, overallProgress float64, status, message, currentQuality string, qualityProgress []QualityProgress, data map[string]interface{}) {
	progressMsg := &ProgressMessage{
		Type:            "progress",
		JobID:           jobID,
		Progress:        overallProgress,
		Status:          status,
		Message:         message,
		CurrentQuality:  currentQuality,
		QualityProgress: qualityProgress,
		Timestamp:       time.Now(),
		Data:            data,
	}

	messageBytes, err := json.Marshal(progressMsg)
	if err != nil {
		log.Printf("Erro ao serializar mensagem de progresso com qualidades: %v", err)
		return
	}

	select {
	case h.jobBroadcast <- &JobMessage{JobID: jobID, Message: messageBytes}:
	default:
		log.Printf("Canal de broadcast cheio, mensagem de progresso com qualidades descartada para job %s", jobID)
	}
}

// SendJobStatus envia atualização de status do job
func (h *Hub) SendJobStatus(jobID, status, message string, data map[string]interface{}) {
	statusMsg := &ProgressMessage{
		Type:      "status",
		JobID:     jobID,
		Status:    status,
		Message:   message,
		Timestamp: time.Now(),
		Data:      data,
	}

	messageBytes, err := json.Marshal(statusMsg)
	if err != nil {
		log.Printf("Erro ao serializar mensagem de status: %v", err)
		return
	}

	select {
	case h.jobBroadcast <- &JobMessage{JobID: jobID, Message: messageBytes}:
	default:
		log.Printf("Canal de broadcast cheio, mensagem de status descartada para job %s", jobID)
	}
}

// GetStats retorna estatísticas do hub
func (h *Hub) GetStats() map[string]interface{} {
	h.mu.RLock()
	defer h.mu.RUnlock()

	jobStats := make(map[string]int)
	for jobID, clients := range h.jobClients {
		jobStats[jobID] = len(clients)
	}

	return map[string]interface{}{
		"total_clients":  len(h.clients),
		"jobs_monitored": len(h.jobClients),
		"job_clients":    jobStats,
	}
}
